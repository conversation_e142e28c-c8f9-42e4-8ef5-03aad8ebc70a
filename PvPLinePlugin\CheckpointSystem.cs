using System;
using System.Collections.Generic;
using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public enum CheckpointState
{
    Neutral,
    FriendlyControlled,
    EnemyControlled,
    Contested
}

public enum CheckpointAlertType
{
    Lost,
    Contested,
    Recaptured
}

public class CheckpointInfo
{
    public Vector3 Position { get; set; }
    public float CaptureRadius { get; set; } = 8.0f; // Typical CC checkpoint radius
    public CheckpointState State { get; set; } = CheckpointState.Neutral;
    public CheckpointState PreviousState { get; set; } = CheckpointState.Neutral;
    public List<IPlayerCharacter> ControllersNearby { get; set; } = [];
    public DateTime LastStateChange { get; set; }
    public int CheckpointIndex { get; set; } // 0, 1, 2 for the three checkpoints
    public string Name => $"CP-{(char)('A' + CheckpointIndex)}";
}

public static class CheckpointDetector
{
    private static readonly List<CheckpointInfo> checkpoints = [];
    
    // Crystalline Conflict map checkpoint positions
    // These are approximate positions - may need adjustment based on actual map data
    private static readonly Dictionary<uint, Vector3[]> territoryCheckpoints = new()
    {
        // Main Crystalline Conflict maps
        [1002] = new Vector3[] // Generic CC Territory ID
        {
            new Vector3(100f, 0f, 0f),   // Checkpoint A (forward)
            new Vector3(0f, 0f, 0f),     // Checkpoint B (middle)
            new Vector3(-100f, 0f, 0f)   // Checkpoint C (back)
        }
    };

    public static List<CheckpointInfo> GetCheckpoints() => checkpoints;

    public static void InitializeCheckpoints(uint territoryType)
    {
        checkpoints.Clear();
        
        if (territoryCheckpoints.TryGetValue(territoryType, out var positions))
        {
            for (int i = 0; i < positions.Length; i++)
            {
                checkpoints.Add(new CheckpointInfo
                {
                    Position = positions[i],
                    CheckpointIndex = i,
                    LastStateChange = DateTime.Now
                });
            }
        }
        else if (IsCrystallineConflictTerritory(territoryType))
        {
            // Fallback: Try to detect checkpoints dynamically
            // This would require more advanced detection methods
            InitializeFallbackCheckpoints();
        }
    }

    private static void InitializeFallbackCheckpoints()
    {
        // Fallback checkpoint positions for unknown CC maps
        // These are generic positions that might work for most CC maps
        for (int i = 0; i < 3; i++)
        {
            checkpoints.Add(new CheckpointInfo
            {
                Position = new Vector3((i - 1) * 50f, 0f, 0f), // Spread along X axis
                CheckpointIndex = i,
                LastStateChange = DateTime.Now
            });
        }
    }

    public static bool IsCrystallineConflictTerritory(uint territoryType)
    {
        // Known Crystalline Conflict territory IDs
        return territoryType switch
        {
            1002 => true, // Main CC territory
            // Add more CC territory IDs as they're discovered
            _ => false
        };
    }

    public static void UpdateCheckpointControl()
    {
        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        foreach (var checkpoint in checkpoints)
        {
            var nearbyPlayers = GetPlayersNearCheckpoint(checkpoint);
            var friendlyCount = 0;
            var enemyCount = 0;

            checkpoint.ControllersNearby.Clear();

            foreach (var player in nearbyPlayers)
            {
                checkpoint.ControllersNearby.Add(player);
                
                if (IsAllyPlayer(localPlayer, player))
                    friendlyCount++;
                else
                    enemyCount++;
            }

            // Store previous state
            checkpoint.PreviousState = checkpoint.State;
            
            // Determine new checkpoint state
            var newState = DetermineCheckpointState(friendlyCount, enemyCount, checkpoint.State);
            
            if (newState != checkpoint.State)
            {
                checkpoint.State = newState;
                checkpoint.LastStateChange = DateTime.Now;
                
                // Could trigger audio alerts here
                OnCheckpointStateChanged(checkpoint);
            }
        }
    }

    private static List<IPlayerCharacter> GetPlayersNearCheckpoint(CheckpointInfo checkpoint)
    {
        var nearbyPlayers = new List<IPlayerCharacter>();
        
        foreach (var obj in Plugin.ObjectTable)
        {
            if (obj is IPlayerCharacter player)
            {
                var distance = Vector3.Distance(player.Position, checkpoint.Position);
                if (distance <= checkpoint.CaptureRadius)
                {
                    nearbyPlayers.Add(player);
                }
            }
        }
        
        return nearbyPlayers;
    }

    private static CheckpointState DetermineCheckpointState(int friendlyCount, int enemyCount, CheckpointState currentState)
    {
        if (friendlyCount > 0 && enemyCount > 0)
        {
            return CheckpointState.Contested;
        }
        else if (friendlyCount > 0)
        {
            return CheckpointState.FriendlyControlled;
        }
        else if (enemyCount > 0)
        {
            return CheckpointState.EnemyControlled;
        }
        else
        {
            // No one nearby, maintain current state or go neutral
            return currentState == CheckpointState.Contested ? CheckpointState.Neutral : currentState;
        }
    }

    private static bool IsAllyPlayer(IPlayerCharacter localPlayer, IPlayerCharacter otherPlayer)
    {
        // In CC, this would need more sophisticated alliance detection
        // For now, assume non-party members are enemies
        // This is a simplified implementation
        return false; // Placeholder - would need proper alliance detection
    }

    public static CheckpointInfo? GetNearestCheckpoint(Vector3 position, float maxDistance = 15.0f)
    {
        CheckpointInfo? nearest = null;
        var nearestDistance = float.MaxValue;

        foreach (var checkpoint in checkpoints)
        {
            var distance = Vector3.Distance(position, checkpoint.Position);
            if (distance < nearestDistance && distance <= maxDistance)
            {
                nearest = checkpoint;
                nearestDistance = distance;
            }
        }

        return nearest;
    }

    public static int GetCheckpointPriority(CheckpointInfo checkpoint)
    {
        // Middle checkpoint (B) is usually most important
        return checkpoint.CheckpointIndex switch
        {
            1 => 3, // Middle checkpoint (B) highest priority
            0 => 2, // Forward checkpoint (A) medium priority  
            2 => 1, // Back checkpoint (C) lowest priority
            _ => 0
        };
    }

    public static float CalculateCheckpointThreat(IPlayerCharacter enemy, CheckpointInfo checkpoint)
    {
        var baseThreat = JobHelper.GetRolePriority(JobHelper.GetPlayerRole(enemy));
        var checkpointMultiplier = GetCheckpointPriority(checkpoint) * 0.5f;
        var contestedMultiplier = checkpoint.State == CheckpointState.Contested ? 1.5f : 1.0f;
        
        return baseThreat * (1.0f + checkpointMultiplier) * contestedMultiplier;
    }

    private static void OnCheckpointStateChanged(CheckpointInfo checkpoint)
    {
        // This could trigger audio alerts or other notifications
        // For now, just log the change
        Plugin.Log.Information($"Checkpoint {checkpoint.Name} changed from {checkpoint.PreviousState} to {checkpoint.State}");
    }
}
