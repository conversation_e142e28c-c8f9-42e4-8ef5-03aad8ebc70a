# PvP Line Plugin

A FFXIV Dalamud plugin that draws lines from your character to enemy players in PvP zones, helping you track opponents during combat.

## Features

- **Visual Enemy Tracking**: Draw lines from your character to enemy players
- **Customizable Appearance**: Adjust line color, thickness, and transparency
- **Distance Control**: Set maximum distance for line drawing and display distance text
- **PvP Zone Detection**: Automatically activates only in PvP zones (configurable)
- **Combat Mode**: Option to show lines only during combat
- **Performance Options**: Adjustable update frequency to balance responsiveness and performance
- **Player Information**: Optional display of enemy player names

## Installation

1. Make sure you have [XIVLauncher](https://goatcorp.github.io/) and [<PERSON><PERSON><PERSON>](https://github.com/goatcorp/Dalamud) installed
2. Build the plugin using Visual Studio 2022 or JetBrains Rider
3. Copy the built DLL to your Dalamud dev plugins folder
4. Enable the plugin in the Dalamud plugin installer

## Building

### Prerequisites

- [.NET 8.0 SDK](https://dotnet.microsoft.com/download/dotnet/8.0)
- Visual Studio 2022 or JetBrains Rider
- <PERSON><PERSON><PERSON><PERSON><PERSON> and <PERSON><PERSON><PERSON> installed

### Build Steps

1. Clone or download this repository
2. Open `PvPLinePlugin.sln` in your IDE
3. Build the solution (Debug or Release)
4. The plugin DLL will be output to `PvPLinePlugin/bin/x64/Debug/` or `PvPLinePlugin/bin/x64/Release/`

## Usage

### Commands

- `/pvplines` - Open the main plugin window
- `/pvplines config` - Open the configuration window

### Configuration Options

**Line Appearance**
- Line Color: RGB color picker for line color
- Line Thickness: Adjust line thickness (1-10 pixels)

**Distance Settings**
- Max Distance: Maximum distance to draw lines (10-100 yalms)
- Show Distance Text: Display distance numbers on lines

**PvP Settings**
- Only Show in PvP Zones: Restrict functionality to PvP areas only
- Only Show During Combat: Only display lines when in combat
- Show Enemy Player Names: Display player names near enemies

**Performance**
- Update Frequency: How often to refresh line positions (16-200ms)

## Supported PvP Zones

The plugin automatically detects the following PvP zones:

**Frontlines**
- Seal Rock (Seize)
- The Fields of Glory (Shatter)
- The Borderland Ruins (Secure)

**Rival Wings**
- Astragalos
- Hidden Gorge

**Crystalline Conflict**
- All Crystalline Conflict maps

**The Wolves' Den**
- The Wolves' Den Pier

## Notes

- The plugin only works when logged into the game
- Enemy detection is based on party/alliance membership
- Lines are drawn using screen coordinates and will update as you move
- Performance impact is minimal with default settings

## Troubleshooting

**Lines not appearing:**
- Check that the plugin is enabled in configuration
- Verify you're in a supported PvP zone (if "Only Show in PvP Zones" is enabled)
- Make sure there are enemy players within the configured distance range

**Performance issues:**
- Increase the update frequency (higher ms value) in configuration
- Reduce the maximum distance setting
- Disable distance text and player names if not needed

## License

This project is licensed under the AGPL-3.0 License - see the LICENSE file for details.

## Contributing

Feel free to submit issues and pull requests to improve the plugin!
