using System.Numerics;
using Dalamud.Game.ClientState.Objects.SubKinds;

namespace PvPLinePlugin;

public enum PlayerRole
{
    Tank,
    Healer,
    MeleeDPS,
    PhysicalRangedDPS,
    MagicalRangedDPS,
    Unknown
}

public static class JobHelper
{
    public static PlayerRole GetPlayerRole(IPlayerCharacter player)
    {
        var jobId = player.ClassJob.Id;
        
        return jobId switch
        {
            // Tanks
            1 => PlayerRole.Tank,    // GLA
            3 => PlayerRole.Tank,    // MRD
            19 => PlayerRole.Tank,   // PLD
            21 => PlayerRole.Tank,   // WAR
            32 => PlayerRole.Tank,   // DRK
            37 => PlayerRole.Tank,   // GNB
            
            // Healers
            6 => PlayerRole.Healer,  // CNJ
            24 => PlayerRole.Healer, // WHM
            28 => PlayerRole.Healer, // SCH
            33 => PlayerRole.Healer, // AST
            40 => PlayerRole.Healer, // SGE
            
            // Melee DPS
            2 => PlayerRole.MeleeDPS,  // PGL
            4 => PlayerRole.MeleeDPS,  // LNC
            12 => PlayerRole.MeleeDPS, // ROG
            20 => PlayerRole.MeleeDPS, // MNK
            22 => PlayerRole.MeleeDPS, // DRG
            29 => PlayerRole.MeleeDPS, // NIN
            34 => PlayerRole.MeleeDPS, // SAM
            39 => PlayerRole.MeleeDPS, // RPR
            42 => PlayerRole.MeleeDPS, // VPR
            
            // Physical Ranged DPS
            5 => PlayerRole.PhysicalRangedDPS,  // ARC
            23 => PlayerRole.PhysicalRangedDPS, // BRD
            31 => PlayerRole.PhysicalRangedDPS, // MCH
            38 => PlayerRole.PhysicalRangedDPS, // DNC
            
            // Magical Ranged DPS
            7 => PlayerRole.MagicalRangedDPS,  // THM
            25 => PlayerRole.MagicalRangedDPS, // BLM
            26 => PlayerRole.MagicalRangedDPS, // ACN
            27 => PlayerRole.MagicalRangedDPS, // SMN
            35 => PlayerRole.MagicalRangedDPS, // RDM
            41 => PlayerRole.MagicalRangedDPS, // PCT
            
            _ => PlayerRole.Unknown
        };
    }
    
    public static string GetJobAbbreviation(IPlayerCharacter player)
    {
        var jobId = player.ClassJob.Id;
        
        return jobId switch
        {
            // Tanks
            1 => "GLA",
            3 => "MRD", 
            19 => "PLD",
            21 => "WAR",
            32 => "DRK",
            37 => "GNB",
            
            // Healers
            6 => "CNJ",
            24 => "WHM",
            28 => "SCH",
            33 => "AST",
            40 => "SGE",
            
            // Melee DPS
            2 => "PGL",
            4 => "LNC",
            12 => "ROG",
            20 => "MNK",
            22 => "DRG",
            29 => "NIN",
            34 => "SAM",
            39 => "RPR",
            42 => "VPR",
            
            // Physical Ranged DPS
            5 => "ARC",
            23 => "BRD",
            31 => "MCH",
            38 => "DNC",
            
            // Magical Ranged DPS
            7 => "THM",
            25 => "BLM",
            26 => "ACN",
            27 => "SMN",
            35 => "RDM",
            41 => "PCT",
            
            _ => "???"
        };
    }
    
    public static string GetRoleAbbreviation(PlayerRole role)
    {
        return role switch
        {
            PlayerRole.Tank => "TANK",
            PlayerRole.Healer => "HEAL",
            PlayerRole.MeleeDPS => "MDPS",
            PlayerRole.PhysicalRangedDPS => "PDPS",
            PlayerRole.MagicalRangedDPS => "CDPS",
            _ => "???"
        };
    }
    
    public static Vector4 GetRoleColor(PlayerRole role)
    {
        return role switch
        {
            PlayerRole.Tank => new Vector4(0.0f, 0.4f, 1.0f, 1.0f),           // Blue
            PlayerRole.Healer => new Vector4(0.0f, 0.8f, 0.0f, 1.0f),         // Green
            PlayerRole.MeleeDPS => new Vector4(1.0f, 0.0f, 0.0f, 1.0f),       // Red
            PlayerRole.PhysicalRangedDPS => new Vector4(1.0f, 0.5f, 0.0f, 1.0f), // Orange
            PlayerRole.MagicalRangedDPS => new Vector4(0.8f, 0.0f, 0.8f, 1.0f),  // Purple
            _ => new Vector4(0.7f, 0.7f, 0.7f, 1.0f)                          // Gray
        };
    }
    
    public static int GetRolePriority(PlayerRole role)
    {
        // Higher priority = more important to track
        return role switch
        {
            PlayerRole.Healer => 3,              // Highest priority
            PlayerRole.Tank => 2,                // High priority
            PlayerRole.MeleeDPS => 1,            // Medium priority
            PlayerRole.PhysicalRangedDPS => 1,   // Medium priority
            PlayerRole.MagicalRangedDPS => 1,    // Medium priority
            _ => 0                               // Lowest priority
        };
    }
}
