using System;
using System.Numerics;
using Dalamud.Interface.Windowing;
using ImGuiNET;

namespace PvPLinePlugin.Windows;

public class ConfigWindow : Window, IDisposable
{
    private Configuration Configuration;
    private Plugin Plugin;

    public ConfigWindow(Plugin plugin) : base("PvP Line Plugin Configuration###PvPLinePluginConfig")
    {
        Flags = ImGuiWindowFlags.NoCollapse;

        Size = new Vector2(500, 700);
        SizeCondition = ImGuiCond.FirstUseEver;

        Configuration = plugin.Configuration;
        Plugin = plugin;
    }

    public void Dispose() { }

    public override void Draw()
    {
        var enabled = Configuration.Enabled;
        if (ImGui.Checkbox("Enable PvP Lines", ref enabled))
        {
            Configuration.Enabled = enabled;
            Configuration.Save();
        }
        if (ImGui.IsItemHovered())
        {
            ImGui.SetTooltip("Master on/off switch for the entire plugin.\nDisable when you don't want any lines displayed.");
        }

        ImGui.Separator();

        // Line Appearance Settings
        if (ImGui.CollapsingHeader("Line Appearance"))
        {
            ImGui.TextWrapped("Customize how the lines look on your screen.");
            ImGui.Spacing();

            var lineColor = Configuration.LineColor;
            if (ImGui.ColorEdit4("Line Color", ref lineColor))
            {
                Configuration.LineColor = lineColor;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Choose the color and transparency of lines.\nRecommended: Red for aggression, Yellow for visibility, Blue for subtlety.\nLower alpha (transparency) makes lines less intrusive.");
            }

            var lineThickness = Configuration.LineThickness;
            if (ImGui.SliderFloat("Line Thickness", ref lineThickness, 1.0f, 10.0f))
            {
                Configuration.LineThickness = lineThickness;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Controls how thick the lines appear.\n1-2px: Subtle, minimal\n3-4px: Good balance\n5-7px: High visibility\n8-10px: Maximum visibility but may be distracting");
            }
        }

        // Distance Settings
        if (ImGui.CollapsingHeader("Distance Settings"))
        {
            ImGui.TextWrapped("Control how far lines reach and distance information display.");
            ImGui.Spacing();

            var maxDistance = Configuration.MaxDistance;
            if (ImGui.SliderFloat("Max Distance (yalms)", ref maxDistance, 10.0f, 100.0f))
            {
                Configuration.MaxDistance = maxDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Only draws lines to enemies within this distance.\n20-30y: Close combat (melee DPS, tanks)\n40-50y: Balanced for most jobs\n60-80y: Long-range (casters, ranged DPS)\n100y: Maximum awareness (may cause clutter)");
            }

            var showDistance = Configuration.ShowDistance;
            if (ImGui.Checkbox("Show Distance Text", ref showDistance))
            {
                Configuration.ShowDistance = showDistance;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays actual distance in yalms next to each line.\nUseful for learning optimal engagement ranges.\nDisable to reduce screen clutter.");
            }
        }

        // PvP Settings
        if (ImGui.CollapsingHeader("PvP Settings"))
        {
            ImGui.TextWrapped("Configure when and how the plugin activates in PvP content.");
            ImGui.Spacing();

            var onlyInPvP = Configuration.OnlyInPvP;
            if (ImGui.Checkbox("Only Show in PvP Zones", ref onlyInPvP))
            {
                Configuration.OnlyInPvP = onlyInPvP;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Restricts plugin to PvP areas only (Frontlines, Rival Wings, Crystalline Conflict, Wolves' Den).\nRecommended: Keep enabled to avoid marking friendly players as enemies in PvE content.");
            }

            var showInCombatOnly = Configuration.ShowInCombatOnly;
            if (ImGui.Checkbox("Only Show During Combat", ref showInCombatOnly))
            {
                Configuration.ShowInCombatOnly = showInCombatOnly;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Lines only appear when you're actively in combat (hotbars turn red).\nEnable: Reduces visual noise during downtime\nDisable: Constant enemy awareness and positioning");
            }

            var showPlayerNames = Configuration.ShowPlayerNames;
            if (ImGui.Checkbox("Show Enemy Player Names", ref showPlayerNames))
            {
                Configuration.ShowPlayerNames = showPlayerNames;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays character names above enemy players.\nUseful for recognizing specific players and coordinating with team.\nDisable to reduce screen clutter.");
            }

            var showPlayerJobs = Configuration.ShowPlayerJobs;
            if (ImGui.Checkbox("Show Enemy Jobs/Roles", ref showPlayerJobs))
            {
                Configuration.ShowPlayerJobs = showPlayerJobs;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Displays job or role information above enemy players.\nHelps identify threats: Healers (priority targets), Tanks (high HP), DPS types.\nEssential for tactical PvP play.");
            }

            if (Configuration.ShowPlayerJobs)
            {
                ImGui.Indent();
                var showJobIcons = Configuration.ShowJobIcons;
                if (ImGui.Checkbox("Show Job Abbreviations (vs Role Names)", ref showJobIcons))
                {
                    Configuration.ShowJobIcons = showJobIcons;
                    Configuration.Save();
                }
                if (ImGui.IsItemHovered())
                {
                    ImGui.SetTooltip("Job Abbreviations: WHM, PLD, NIN, etc.\nRole Names: HEAL, TANK, MDPS, etc.\nJob abbreviations are more specific but take more space.");
                }
                ImGui.Unindent();
            }

            var colorCodeByRole = Configuration.ColorCodeByRole;
            if (ImGui.Checkbox("Color Lines by Role", ref colorCodeByRole))
            {
                Configuration.ColorCodeByRole = colorCodeByRole;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Colors lines based on enemy role:\nBlue = Tanks, Green = Healers, Red = Melee DPS\nOrange = Ranged Physical DPS, Purple = Magical DPS\nOverrides the custom line color setting when enabled.");
            }
        }

        // Performance Settings
        if (ImGui.CollapsingHeader("Performance"))
        {
            ImGui.TextWrapped("Adjust performance vs responsiveness balance.");
            ImGui.Spacing();

            var updateFreq = Configuration.UpdateFrequency;
            if (ImGui.SliderInt("Update Frequency (ms)", ref updateFreq, 16, 200))
            {
                Configuration.UpdateFrequency = updateFreq;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("How often the plugin refreshes line positions.\n16-30ms: Competitive play, smooth tracking\n60ms: Good balance for most users\n100-120ms: Older computers or multitasking\n150-200ms: Minimal performance impact");
            }

            ImGui.Spacing();
            ImGui.TextColored(new Vector4(0.7f, 0.7f, 0.7f, 1.0f), "Lower values = more responsive but higher CPU usage");
            ImGui.TextColored(new Vector4(0.7f, 0.7f, 0.7f, 1.0f), "Higher values = less responsive but better performance");
        }

        ImGui.Separator();
        ImGui.Spacing();

        // Quick Setup Presets
        if (ImGui.CollapsingHeader("Quick Setup Presets"))
        {
            ImGui.TextWrapped("Click a preset to quickly configure settings for your playstyle.");
            ImGui.Spacing();

            if (ImGui.Button("Aggressive Melee (NIN, MNK, DRG)"))
            {
                Configuration.LineColor = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Bright Red
                Configuration.LineThickness = 4.0f;
                Configuration.MaxDistance = 30.0f;
                Configuration.ShowDistance = true;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = true;
                Configuration.ShowPlayerNames = false;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = false; // Show roles for quick identification
                Configuration.ColorCodeByRole = true;
                Configuration.UpdateFrequency = 30;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Optimized for close-range combat with high visibility lines.");
            }

            ImGui.SameLine();
            if (ImGui.Button("Ranged DPS (BLM, SMN, BRD)"))
            {
                Configuration.LineColor = new Vector4(1.0f, 1.0f, 0.0f, 0.8f); // Yellow
                Configuration.LineThickness = 2.5f;
                Configuration.MaxDistance = 60.0f;
                Configuration.ShowDistance = true;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = false;
                Configuration.ShowPlayerNames = true;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = true; // Show specific jobs for targeting
                Configuration.ColorCodeByRole = true;
                Configuration.UpdateFrequency = 60;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Longer range with constant awareness and target identification.");
            }

            if (ImGui.Button("Tank/Support"))
            {
                Configuration.LineColor = new Vector4(0.0f, 0.5f, 1.0f, 0.7f); // Blue
                Configuration.LineThickness = 3.0f;
                Configuration.MaxDistance = 45.0f;
                Configuration.ShowDistance = false;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = false;
                Configuration.ShowPlayerNames = true;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = false; // Show roles for team awareness
                Configuration.ColorCodeByRole = true;
                Configuration.UpdateFrequency = 80;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Balanced settings for positioning and team awareness.");
            }

            ImGui.SameLine();
            if (ImGui.Button("Performance Focused"))
            {
                Configuration.LineColor = new Vector4(1.0f, 0.0f, 0.0f, 0.6f); // Semi-transparent Red
                Configuration.LineThickness = 2.0f;
                Configuration.MaxDistance = 35.0f;
                Configuration.ShowDistance = false;
                Configuration.OnlyInPvP = true;
                Configuration.ShowInCombatOnly = true;
                Configuration.ShowPlayerNames = false;
                Configuration.ShowPlayerJobs = true;
                Configuration.ShowJobIcons = false; // Roles only for minimal text
                Configuration.ColorCodeByRole = false; // Use single color for performance
                Configuration.UpdateFrequency = 120;
                Configuration.Save();
            }
            if (ImGui.IsItemHovered())
            {
                ImGui.SetTooltip("Minimal performance impact while maintaining functionality.");
            }
        }
    }
}
