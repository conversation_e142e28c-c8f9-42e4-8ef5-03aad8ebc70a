using System;
using System.Collections.Generic;
using System.Linq;
using System.Numerics;
using Dalamud.Game.ClientState.Conditions;
using Dalamud.Game.ClientState.Objects.Types;
using Dalamud.Game.ClientState.Objects.SubKinds;
using Dalamud.Plugin.Services;
using ImGuiNET;

namespace PvPLinePlugin;

public class PvPOverlay : IDisposable
{
    private readonly Plugin plugin;
    private readonly Configuration config;
    private DateTime lastUpdate = DateTime.MinValue;

    public PvPOverlay(Plugin plugin)
    {
        this.plugin = plugin;
        this.config = plugin.Configuration;
        
        // Subscribe to the UI draw event
        Plugin.PluginInterface.UiBuilder.Draw += DrawOverlay;
    }

    public void Dispose()
    {
        Plugin.PluginInterface.UiBuilder.Draw -= DrawOverlay;
    }

    private void DrawOverlay()
    {
        // Check if we should draw anything
        if (!ShouldDrawLines()) return;

        // Throttle updates based on configuration
        var now = DateTime.Now;
        if ((now - lastUpdate).TotalMilliseconds < config.UpdateFrequency)
            return;
        
        lastUpdate = now;

        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return;

        var enemyPlayers = GetEnemyPlayers(localPlayer);
        
        foreach (var enemy in enemyPlayers)
        {
            DrawLineToEnemy(localPlayer, enemy);
        }
    }

    private bool ShouldDrawLines()
    {
        if (!config.Enabled) return false;
        
        var localPlayer = Plugin.ClientState.LocalPlayer;
        if (localPlayer == null) return false;

        // Check if only in PvP zones
        if (config.OnlyInPvP && !IsPvPTerritory(Plugin.ClientState.TerritoryType))
            return false;

        // Check if only during combat
        if (config.ShowInCombatOnly && !Plugin.Condition[ConditionFlag.InCombat])
            return false;

        return true;
    }

    private List<GameObject> GetEnemyPlayers(GameObject localPlayer)
    {
        var enemies = new List<GameObject>();

        foreach (var obj in Plugin.ObjectTable)
        {
            if (obj.ObjectKind != Dalamud.Game.ClientState.Objects.Enums.ObjectKind.Player) continue;
            if (obj.ObjectId == localPlayer.ObjectId) continue; // Skip self

            // Check if it's an enemy player
            if (!IsEnemyPlayer(localPlayer, obj)) continue;

            // Check distance
            var distance = Vector3.Distance(localPlayer.Position, obj.Position);
            if (distance > config.MaxDistance) continue;

            enemies.Add(obj);
        }

        return enemies;
    }

    private bool IsEnemyPlayer(GameObject localPlayer, GameObject otherPlayer)
    {
        // In PvP, players from different Grand Companies or teams are enemies
        // This is a simplified check - you might need more sophisticated logic
        // depending on the specific PvP mode
        
        // Check if they're in different alliances/parties
        if (localPlayer.StatusFlags.HasFlag(Dalamud.Game.ClientState.Objects.Enums.StatusFlags.PartyMember) &&
            otherPlayer.StatusFlags.HasFlag(Dalamud.Game.ClientState.Objects.Enums.StatusFlags.PartyMember))
        {
            // If both are in parties, they might be allies
            return false;
        }
        
        // In most PvP content, if they're not in your party/alliance, they're enemies
        // This is a basic implementation - you might want to refine this
        return true;
    }

    private void DrawLineToEnemy(IPlayerCharacter localPlayer, IPlayerCharacter enemy)
    {
        // Convert world positions to screen coordinates
        if (!Plugin.GameGui.WorldToScreen(localPlayer.Position, out var localScreenPos)) return;
        if (!Plugin.GameGui.WorldToScreen(enemy.Position, out var enemyScreenPos)) return;

        var drawList = ImGui.GetBackgroundDrawList();
        
        // Convert Vector2 to Vector2 for ImGui
        var startPos = new Vector2(localScreenPos.X, localScreenPos.Y);
        var endPos = new Vector2(enemyScreenPos.X, enemyScreenPos.Y);
        
        // Convert color to uint
        var color = ImGui.ColorConvertFloat4ToU32(config.LineColor);
        
        // Draw the line
        drawList.AddLine(startPos, endPos, color, config.LineThickness);
        
        // Draw distance text if enabled
        if (config.ShowDistance)
        {
            var distance = Vector3.Distance(localPlayer.Position, enemy.Position);
            var distanceText = $"{distance:F1}y";
            
            // Position text at the midpoint of the line
            var midPoint = new Vector2(
                (startPos.X + endPos.X) / 2,
                (startPos.Y + endPos.Y) / 2
            );
            
            drawList.AddText(midPoint, ImGui.ColorConvertFloat4ToU32(new Vector4(1, 1, 1, 1)), distanceText);
        }
        
        // Draw player name if enabled
        if (config.ShowPlayerNames)
        {
            var namePos = new Vector2(enemyScreenPos.X, enemyScreenPos.Y - 20);
            drawList.AddText(namePos, ImGui.ColorConvertFloat4ToU32(new Vector4(1, 1, 1, 1)), enemy.Name.TextValue);
        }
    }

    private bool IsPvPTerritory(uint territoryType)
    {
        // Common PvP territory IDs
        return territoryType switch
        {
            // Frontlines
            376 => true, // Seal Rock
            554 => true, // The Fields of Glory (Shatter)
            692 => true, // The Borderland Ruins (Secure)
            
            // Rival Wings
            691 => true, // Astragalos
            789 => true, // Hidden Gorge
            
            // Crystalline Conflict
            1002 => true, // Crystalline Conflict
            
            // The Wolves' Den
            250 => true, // The Wolves' Den Pier
            
            _ => false
        };
    }
}
