using Dalamud.Configuration;
using Dalamud.Plugin;
using System;
using System.Numerics;

namespace PvPLinePlugin;

[Serializable]
public class Configuration : IPluginConfiguration
{
    public int Version { get; set; } = 0;

    public bool Enabled { get; set; } = true;
    
    // Line appearance settings
    public Vector4 LineColor { get; set; } = new Vector4(1.0f, 0.0f, 0.0f, 1.0f); // Red
    public float LineThickness { get; set; } = 2.0f;
    
    // Distance settings
    public float MaxDistance { get; set; } = 50.0f; // Maximum distance to draw lines (in yalms)
    public bool ShowDistance { get; set; } = true; // Show distance text
    
    // PvP zone settings
    public bool OnlyInPvP { get; set; } = true; // Only show lines in PvP zones
    
    // Performance settings
    public int UpdateFrequency { get; set; } = 60; // Update frequency in milliseconds
    
    // UI settings
    public bool ShowInCombatOnly { get; set; } = false; // Only show when in combat
    public bool ShowPlayerNames { get; set; } = false; // Show enemy player names
    public bool ShowPlayerJobs { get; set; } = true; // Show enemy player jobs/roles
    public bool ShowJobIcons { get; set; } = false; // Show job icons instead of text
    public bool ColorCodeByRole { get; set; } = true; // Color lines by role (Tank=Blue, Healer=Green, DPS=Red)

    // Low health settings
    public bool ShowLowHealthIndicator { get; set; } = true; // Show indicator for low health enemies
    public float LowHealthThreshold { get; set; } = 25.0f; // Health percentage threshold for "killable" indicator
    public Vector4 LowHealthLineColor { get; set; } = new Vector4(1.0f, 0.8f, 0.0f, 1.0f); // Gold/Yellow for low health
    public float LowHealthLineThickness { get; set; } = 5.0f; // Thicker lines for low health targets
    public bool ShowHealthPercentage { get; set; } = true; // Show actual health percentage
    public bool PulseKillableTargets { get; set; } = true; // Make killable target lines pulse/animate

    // Checkpoint control settings (Crystalline Conflict)
    public bool ShowCheckpointControl { get; set; } = true; // Show enemies controlling checkpoints
    public Vector4 EnemyCheckpointColor { get; set; } = new Vector4(1.0f, 0.3f, 0.3f, 1.0f); // Red for enemy control
    public Vector4 ContestedCheckpointColor { get; set; } = new Vector4(1.0f, 0.8f, 0.0f, 1.0f); // Orange for contested
    public Vector4 FriendlyCheckpointColor { get; set; } = new Vector4(0.3f, 1.0f, 0.3f, 1.0f); // Green for friendly
    public bool ShowCheckpointLabels { get; set; } = true; // Show CP-A, CP-B, CP-C labels
    public bool PulseContestedCheckpoints { get; set; } = true; // Pulse contested checkpoint indicators
    public float CheckpointDetectionRadius { get; set; } = 8.0f; // Radius for checkpoint control detection
    public float CheckpointLineThicknessMultiplier { get; set; } = 1.5f; // Line thickness multiplier for checkpoint controllers

    // the below exist just to make saving less cumbersome
    public void Save()
    {
        Plugin.PluginInterface.SavePluginConfig(this);
    }
}
